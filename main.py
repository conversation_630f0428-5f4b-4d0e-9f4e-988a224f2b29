
import random
import matplotlib.pyplot as plt
import numpy as np
import scipy.stats as stats

n_sides = int(input("Enter number of n Sides for the dices\n"))
n_dice = int(input("Enter Number of Dice\n"))
throws = int(input("Enter Throws\n"))

list_of_throws = []
sum_of_throws = np.array([])

def throw_dices(n_sides, n_dice):
    return [random.randint(1, n_sides) for i in range(n_dice)]

for i in range(0, throws):
    dice_roll = throw_dices(n_sides, n_dice)
    list_of_throws.append(dice_roll)
    sum_of_throws = np.append(sum_of_throws, sum(dice_roll))

mu = np.mean(sum_of_throws)
sigma = np.std(sum_of_throws)
x = np.linspace(min(sum_of_throws), max(sum_of_throws), 100)

bin_width = 1  # since we're dealing with integer sums
pdf = stats.norm.pdf(x, mu, sigma) * throws * bin_width
plt.hist(sum_of_throws, bins=range(n_dice, n_dice*n_sides+2), edgecolor='black')
plt.plot(x, pdf, 'r-', linewidth=2, label='Gaussian Distribution')

plt.title("Histogram of Dice Sums with Gaussian Distribution Overlay")
plt.xlabel("Sum")
plt.ylabel("Frequency")
plt.legend()
plt.show()