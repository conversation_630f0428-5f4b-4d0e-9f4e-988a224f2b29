import pandas as pd
import numpy as np


# Setting a seed so the example is reproducible
np.random.seed(4272018)

df = pd.DataFrame(np.random.randint(low= 0, high= 20, size= (5, 2)),
                  columns= ['Commercials Watched', 'Product Purchases'])

df.iloc[2, 0] = np.nan  # setter et NaN i 'Commercials Watched'
df.iloc[4, 1] = np.nan  # setter et NaN i 'Product Purchases'


var = df.var(skipna=True) #Variance
cov = df.cov() #Covariance
corr = df.corr() #Correlation

print(df)

print("------------------------------------------------")
print("Dette er variance: \n", var)




print("Dette er covariance: \n", cov)



print("Dette er correlation: \n", corr)
